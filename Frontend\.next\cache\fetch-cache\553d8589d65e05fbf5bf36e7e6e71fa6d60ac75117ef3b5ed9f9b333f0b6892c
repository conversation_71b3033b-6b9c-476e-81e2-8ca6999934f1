{"kind": "FETCH", "data": {"headers": {"access-control-allow-credentials": "true", "access-control-allow-origin": "", "connection": "keep-alive", "content-length": "12909", "content-security-policy": "script-src 'self' 'unsafe-inline' cdn.marutitech.com;img-src 'self' data: strapi.io cdn.marutitech.com storage.googleapis.com cdn-gcp.new.marutitech.com;media-src cdn.marutitech.com cdn-gcp.new.marutitech.com blob:;connect-src 'self' https:;default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';style-src 'self' https: 'unsafe-inline'", "content-type": "application/json; charset=utf-8", "date": "Mon, 04 Aug 2025 11:45:55 GMT", "referrer-policy": "no-referrer", "server": "nginx/1.24.0", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-powered-by": "Strapi <strapi.io>"}, "body": "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", "status": 200, "url": "https://dev-content.marutitech.com/api/footer?populate=sector_row.Sublinks,pages_row.Sublinks,terms_and_condition_section,company_logo_section.image,company_logo_section.Copyright,company_logo_section.social_platforms.image"}, "revalidate": 31536000, "tags": []}