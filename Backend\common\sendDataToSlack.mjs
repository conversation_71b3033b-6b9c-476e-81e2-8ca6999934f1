import { getConfigValue } from "./ssmConfig.mjs";

/**
 * Send form data to Slack webhook
 * @param formData - The form data to send
 * @param webhookUrl - Optional webhook URL (will use SSM config if not provided)
 * @param extraText - Optional extra text for the message
 */
const sendToSlack = async (formData, webhookUrl, extraText = "") => {
  try {
    // If no webhook URL provided, determine which one to use based on extraText
    if (!webhookUrl) {
      if (extraText) {
        // This is a failure notification
        webhookUrl = await getConfigValue("SLACK_FAILURE_WEBHOOK_URL");
      } else {
        // This is a success notification
        webhookUrl = await getConfigValue("SLACK_SUCCESS_WEBHOOK_URL");
      }
    }

    if (!webhookUrl) {
      console.error("Slack webhook URL is not defined.");
      return;
    }

    const isAIReadinessForm = formData.secondary_source === "AIReadiness";
    const isCloudMigrationForm =
      formData.secondary_source === "Cloud Migration Cost Calculator";

    const messageTitle = extraText
      ? "🚨 *Form Submission Failed!* ⚠️"
      : isAIReadinessForm
        ? "🤖 *New AI Readiness Submission!* 🤖"
        : isCloudMigrationForm
          ? "☁️ *New Cloud Migration Cost Calculator Submission!* ☁️"
          : "🚀 *New Form Submission!* 🚀";

    const aiFields = `
        • First Name: ${formData.firstName || "N/A"}
        • Last Name: ${formData.lastName || "N/A"}
        • Email Address: ${formData.emailAddress || "N/A"}
        • Phone Number: ${formData.phoneNumber || "N/A"}
        • Company Name: ${formData.companyName || "N/A"}
        • UTM Campaign: ${formData.utm_campaign || "N/A"}
        • UTM Medium: ${formData.utm_medium || "N/A"}
        • UTM Source: ${formData.utm_source || "N/A"}
        • GA 4 User ID: ${formData.ga_4_userid || "N/A"}
        • IP Address: ${formData.ip_address || "N/A"}
        • City: ${formData.city || "N/A"}
        • Country: ${formData.country || "N/A"}
        • Referrer: ${formData.referrer || "N/A"}
        • Clarity: ${formData.clarity || "N/A"}
        • Page URL: ${formData.url || "N/A"}
        • Consent: ${formData.consent ? "Yes" : "No"}
        • Do you have clearly defined business objectives and goals for the AI project? : ${formData.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ || "N/A"}
        • How receptive is your Leadership Team to embracing the changes brought about by AI? : ${formData.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ || "N/A"}
        • Do you have budget allocated for your AI project? : ${formData.do_you_have_budget_allocated_for_your_ai_project_ || "N/A"}
        • Do you have a robust data infrastructure for storage, retrieval, and processing? : ${formData.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ || "N/A"}
        • Which of the below DB tools do you currently use? : ${formData.which_of_the_below_db_tools_do_you_currently_use_ || "N/A"}
        • Is the relevant data for the AI project available and accessible? : ${formData.is_the_relevant_data_for_the_ai_project_available_and_accessible_ || "N/A"}
        • Do you have access to necessary computing resources (CPU, GPU, cloud services)? : ${formData.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ || "N/A"}
        • How would you rate your organization's current IT infrastructure in terms of scalability and flexibility to accommodate the evolving computational needs of AI projects? : ${formData.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib || "N/A"}
        • Does the team have the expertise in data science, machine learning, and AI? : ${formData.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ || "N/A"}
        • Do you have systems in place to monitor the performance and accuracy of AI models post-deployment? : ${formData.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ || "N/A"}
        • Do you have risk management strategies in place for the AI project? : ${formData.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ || "N/A"}
        • Do you have a process in place to measure the impact of the deployment of AI / AI-powered solutions? : ${formData.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions || "N/A"}

        • Strategy & Leadership: ${formData.strategy___leadership || "0"} %
        • Data Readiness & Infrastructure: ${formData.data_readiness___infrastructure || "0"} %
        • Talent & Skills: ${formData.talent___skills || "N/A"} %
        • Execution & Monitoring: ${formData.execution___monitoring || "0"} %
        • Impact Evaluation: ${formData.impact_evaliation || "0"} %
        • Average Score: ${formData.average_of_all_score || "0"} %
        `;

    const cloudMigrationFields = `
        • First Name: ${formData.firstName || formData.firstname || "N/A"}
        • Last Name: ${formData.lastName || formData.lastname || "N/A"}
        • Email Address: ${formData.emailAddress || formData.email || "N/A"}
        • Phone Number: ${formData.phoneNumber || formData.phone || "N/A"}
        • Company Name: ${formData.companyName || formData.company || "N/A"}
        • City: ${formData.city || "N/A"}
        • Country: ${formData.country || "N/A"}
        • Minimum Cost: $${formData.lowerRange || formData.minimum_cost || "N/A"}
        • Maximum Cost: $${formData.upperRange || formData.maximum_migration_cost || "N/A"}
        • Elements to Migrate: ${formData.which_elements_are_you_planning_to_migrate_to_the_cloud || formData.which_elements_are_you_planning_to_migrate_to_the_cloud_ || "N/A"}
        • Number of Servers: ${formData.approximately_how_many_servers_do_you_intend_to_migrate || formData.approximately_how_many_servers_do_you_intend_to_migrate_ || "N/A"}
        • Data Migration Type: ${formData.what_is_the_type_of_data_migration_you_intend_to_do || formData.what_is_the_type_of_data_migration_you_intend_to_do_ || "N/A"}
        • Current Infrastructure: ${formData.what_is_your_current_it_infrastructure_setup || formData.what_is_your_current_it_infrastructure_setup_ || "N/A"}
        • Server Capacity: ${formData.what_is_the_total_capacity_of_your_servers || formData.what_is_the_total_capacity_of_your_servers_ || "N/A"}
        • Current Monthly Cost: ${formData.what_is_the_current_monthly_infrastructure_cost_of_your_current_setup || formData.what_is_the_current_monthly_infrastructure_cost_of_your_current_setup_ || "N/A"}
        • Migration Purpose: ${Array.isArray(formData.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud) ? formData.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud.join(", ") : formData.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud || formData.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud____multiple_choice_allowed_ || "N/A"}
        • Workload Types: ${Array.isArray(formData.what_type_of_workloads_do_you_run) ? formData.what_type_of_workloads_do_you_run.join(", ") : formData.what_type_of_workloads_do_you_run || formData.what_type_of_workloads_do_you_run___multiple_choice_allowed_ || "N/A"}
        • CPU/Memory Usage: ${formData.what_is_the_average_cpu_and_memory_usage_of_your_workloads || formData.what_is_the_average_cpu_and_memory_usage_of_your_workloads_ || "N/A"}
        • High Availability Required: ${formData.do_you_require_high_availability_or_disaster_recovery_for_critical_applications || formData.do_you_require_high_availability_or_disaster_recovery_for_critical_applications_ || "N/A"}
        • Cloud Provider: ${formData.which_cloud_provider_s_are_you_considering || formData.which_cloud_provider_s__are_you_considering_ || "N/A"}
        • Pricing Model: ${formData.do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models || formData.do_you_plan_to_use_reserved_instances__spot_instances__or_pay_as_you_go_pricing_models_ || "N/A"}
        • Cloud Environments: ${Array.isArray(formData.which_cloud_environments_are_you_planning_to_deploy) ? formData.which_cloud_environments_are_you_planning_to_deploy.join(", ") : formData.which_cloud_environments_are_you_planning_to_deploy || formData.which_cloud_environments_are_you_planning_to_deploy___multiple_choice_allowed_ || "N/A"}
        • Compliance Requirements: ${formData.do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet || formData.do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_m || "N/A"}
        • Migration Strategy: ${Array.isArray(formData.what_migration_strategy_do_you_prefer) ? formData.what_migration_strategy_do_you_prefer.join(", ") : formData.what_migration_strategy_do_you_prefer || formData.what_migration_strategy_do_you_prefer_ || "N/A"}
        • Auto Scaling: ${formData.do_you_need_auto_scaling_capabilities_for_cost_optimization || formData.do_you_need_auto_scaling_capabilities_for_cost_optimization_ || "N/A"}
        • Cost Review Frequency: ${formData.how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses || formData.how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses_ || "N/A"}
        • UTM Campaign: ${formData.utm_campaign || "N/A"}
        • UTM Medium: ${formData.utm_medium || "N/A"}
        • UTM Source: ${formData.utm_source || "N/A"}
        • IP Address: ${formData.ip_address || "N/A"}
        • GA4 User ID: ${formData.ga_4_userid || "N/A"}
        • Page URL: ${formData.url || formData.source_url || "N/A"}
        • Referrer: ${formData.referrer || "N/A"}
        • Consent: ${formData.consent ? "Yes" : "No"}
        `;

    const contactFields = `
        • *First Name:* ${formData.firstName || "N/A"}
        • *Last Name:* ${formData.lastName || "N/A"}
        • *Email:* ${formData.emailAddress || "N/A"}
        • *Phone:* ${formData.phoneNumber || "N/A"}
        • *Company:* ${formData.companyName || "N/A"}
        • *How Did You Hear About Us?* ${formData.howDidYouHearAboutUs || "N/A"}
        • *How Can We Help?* ${formData.howCanWeHelpYou || "N/A"}
        • *UTM Campaign:* ${formData.utm_campaign || "N/A"}
        • *UTM Medium:* ${formData.utm_medium || "N/A"}
        • *UTM Source:* ${formData.utm_source || "N/A"}
        • *IP Address:* ${formData.ip_address || "N/A"}
        • *GA4 User ID:* ${formData.ga_4_userid || "N/A"}
        • *City:* ${formData.city || "N/A"}
        • *Country:* ${formData.country || "N/A"}
        • *Secondary Source:* ${formData.secondary_source || "N/A"}
        • *Clarity:* ${formData.clarity || "N/A"}
        • *Page URL:* ${formData.url || "N/A"}
        • *Referrer:* ${formData.referrer || "N/A"}
        • *Consent:* ${formData.consent ? "Yes" : "No"} `;

    const message = {
      text: messageTitle,
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: `${messageTitle}\n\n${isAIReadinessForm ? aiFields : isCloudMigrationForm ? cloudMigrationFields : contactFields}`,
          },
        },
        { type: "divider" },
      ],
    };

    const response = await fetch(webhookUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(message),
    });

    if (!response.ok) {
      console.error(
        "Failed to send Slack notification:",
        await response.text()
      );
    }
  } catch (error) {
    console.error("Error sending message to Slack:", error);
  }
};

export default sendToSlack;
