"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-readiness-audit/page",{

/***/ "(app-pages-browser)/./src/components/QuestionAndAnswers/QuestionAndAnswers.tsx":
/*!******************************************************************!*\
  !*** ./src/components/QuestionAndAnswers/QuestionAndAnswers.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ QuestionAndAnswers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hooks/useMediaQueryState */ \"(app-pages-browser)/./src/hooks/useMediaQueryState.ts\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @styles/breakpoints.module.css */ \"(app-pages-browser)/./src/styles/breakpoints.module.css\");\n/* harmony import */ var _styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./QuestionAndAnswers.module.css */ \"(app-pages-browser)/./src/components/QuestionAndAnswers/QuestionAndAnswers.module.css\");\n/* harmony import */ var _QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction QuestionAndAnswers(param) {\n    let { sectionIndex, sectionQuestions, sectionData, sectionError, handleData, handleError } = param;\n    _s();\n    const isTablet = (0,_hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({\n        query: \"(max-width: \".concat((_styles_breakpoints_module_css__WEBPACK_IMPORTED_MODULE_3___default()[\"breakpoint-xl-1024\"]), \")\")\n    });\n    const [subAnswer, setSubAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (localStorage.getItem(\"subAnswer\") !== null) {\n            return JSON.parse(localStorage.getItem(\"subAnswer\"));\n        }\n        return [\n            [],\n            new Array(3).fill(null)\n        ];\n    });\n    // Keyboard event handler\n    const handleKeyDown = (event, callback)=>{\n        if (event.key === \"Enter\" || event.key === \" \") {\n            event.preventDefault();\n            callback();\n        }\n    };\n    function handleID(i, str, id, sectionIndex, questionIndex) {\n        let newSubAnswer = [\n            ...subAnswer\n        ];\n        newSubAnswer[0][i] = str;\n        newSubAnswer[1][i] = id;\n        let value = 0;\n        for(let j = 0; j < subAnswer[1].length; j++){\n            if (newSubAnswer[1][j] !== null) {\n                value = value + 33.33;\n            }\n        }\n        localStorage.setItem(\"subAnswer\", JSON.stringify(newSubAnswer));\n        setSubAnswer(newSubAnswer);\n        handleError(sectionIndex, questionIndex, false);\n        handleData(sectionIndex, questionIndex, newSubAnswer[0].join(\",\"), Math.round(value));\n    }\n    function setSubQuestion(question, sectionIndex, questionIndex) {\n        const sub_question = question.sub_question;\n        const answers = question.answers;\n        const JSX = [];\n        let answerIndex = 0;\n        for(let i = 0; i < sub_question.length; i++){\n            let arr = [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().sub_question_name),\n                    children: sub_question[i].name\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ];\n            for(let j = 0; j < sub_question[i].value; j++){\n                // Check if answerIndex is within bounds to prevent undefined access\n                if (answerIndex >= answers.length) {\n                    console.warn(\"Answer index \".concat(answerIndex, \" exceeds answers array length \").concat(answers.length));\n                    break;\n                }\n                // Capture the current answerIndex value to avoid closure issues\n                const currentAnswerIndex = answerIndex;\n                const currentAnswer = answers[currentAnswerIndex];\n                let content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: subAnswer[1][i] === currentAnswer.id ? \" \".concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().mcq), \" \").concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().selected_mcq)) : (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().mcq),\n                    htmlFor: currentAnswer.id,\n                    tabIndex: 0,\n                    onKeyDown: (e)=>handleKeyDown(e, ()=>{\n                            handleID(i, currentAnswer.name, Number(currentAnswer.id), sectionIndex, questionIndex);\n                        }),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"radio\",\n                            id: currentAnswer.id,\n                            name: sub_question[i].name,\n                            \"data-name\": currentAnswer.name,\n                            value: currentAnswer.id,\n                            onChange: (e)=>{\n                                handleID(i, e.target.dataset.name, Number(e.target.value), sectionIndex, questionIndex);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this),\n                        currentAnswer.name\n                    ]\n                }, currentAnswer.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this);\n                answerIndex = answerIndex + 1;\n                arr.push(content);\n            }\n            JSX.push(arr);\n        }\n        return JSX;\n    }\n    function getAnswerName(questionIndex, value) {\n        let ind = Math.round(value / 25);\n        // Check if the calculated index is within bounds\n        if (ind >= sectionQuestions[questionIndex].answers.length) {\n            console.warn(\"Answer index \".concat(ind, \" exceeds answers array length \").concat(sectionQuestions[questionIndex].answers.length));\n            return \"\";\n        }\n        return sectionQuestions[questionIndex].answers[ind].name;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: sectionQuestions.map((question, questionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().container),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().question_container),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: sectionError[questionIndex] ? \"\".concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().question_number), \" \").concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().error_message)) : (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().question_number),\n                                children: [\n                                    question.number < 10 ? \"0\" : \"\",\n                                    question.number,\n                                    \".\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().question_name),\n                                children: question.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    question.type === \"mcq\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().mcqs_container),\n                        children: question.sub_question.length !== 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: setSubQuestion(question, sectionIndex, questionIndex)\n                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: question.answers.map((ans, answerIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: sectionData[questionIndex][1] === ans.value ? \" \".concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().mcq), \" \").concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().selected_mcq)) : (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().mcq),\n                                    htmlFor: ans.id,\n                                    tabIndex: 0,\n                                    onKeyDown: (e)=>handleKeyDown(e, ()=>{\n                                            handleError(sectionIndex, questionIndex, false);\n                                            handleData(sectionIndex, questionIndex, ans.name, Number(ans.value));\n                                        }),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            id: ans.id,\n                                            name: question.name,\n                                            value: ans.value,\n                                            onChange: (e)=>{\n                                                handleError(sectionIndex, questionIndex, false);\n                                                handleData(sectionIndex, questionIndex, ans.name, Number(e.target.value));\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 23\n                                        }, this),\n                                        ans.name\n                                    ]\n                                }, answerIndex, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 21\n                                }, this))\n                        }, void 0, false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: !isTablet ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_container),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    id: \"range\",\n                                    name: \"range\",\n                                    min: \"0\",\n                                    max: \"100\",\n                                    step: \"25\",\n                                    className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_input),\n                                    value: sectionData[questionIndex][1],\n                                    onChange: (e)=>{\n                                        handleError(sectionIndex, questionIndex, false);\n                                        handleData(sectionIndex, questionIndex, getAnswerName(questionIndex, e.target.value), Number(e.target.value));\n                                    },\n                                    style: {\n                                        background: \"linear-gradient(to right, #30AD43 0%, #30AD43 \".concat(sectionData[questionIndex][1], \"%, #ccc \").concat(sectionData[questionIndex][1], \"%, #ccc 100%)\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_wrapper),\n                                    children: question.answers.map((ans, answerIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: sectionData[questionIndex][1] === ans.value ? \"\".concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_label), \" \").concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().selected_draggable_label)) : (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_label),\n                                            onClick: (e)=>handleData(sectionIndex, questionIndex, ans.name, Number(ans.value)),\n                                            onKeyDown: (e)=>handleKeyDown(e, ()=>{\n                                                    handleData(sectionIndex, questionIndex, ans.name, Number(ans.value));\n                                                }),\n                                            tabIndex: 0,\n                                            role: \"button\",\n                                            \"aria-pressed\": sectionData[questionIndex][1] === ans.value,\n                                            children: ans.name\n                                        }, answerIndex, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().number_wrapper),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().draggable_container_tablet),\n                                    children: question.answers.map((ans, answerIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: sectionData[questionIndex][1] === ans.value ? \" \".concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().number), \" \").concat((_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().selected_number)) : (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().number),\n                                            htmlFor: ans.id,\n                                            tabIndex: 0,\n                                            onKeyDown: (e)=>handleKeyDown(e, ()=>{\n                                                    handleError(sectionIndex, questionIndex, false);\n                                                    handleData(sectionIndex, questionIndex, ans.name, Number(ans.value));\n                                                }),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"radio\",\n                                                    id: ans.id,\n                                                    name: question.name,\n                                                    value: ans.value,\n                                                    onChange: (e)=>{\n                                                        handleError(sectionIndex, questionIndex, false);\n                                                        handleData(sectionIndex, questionIndex, ans.name, Number(e.target.value));\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 25\n                                                }, this),\n                                                answerIndex + 1\n                                            ]\n                                        }, answerIndex, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_QuestionAndAnswers_module_css__WEBPACK_IMPORTED_MODULE_4___default().number_label),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: question.answers[0].name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: question.answers[question.answers.length - 1].name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, questionIndex, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\components\\\\QuestionAndAnswers\\\\QuestionAndAnswers.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this))\n    }, void 0, false);\n}\n_s(QuestionAndAnswers, \"BWCDLdKwPqTg4kUr2qZF8w8Vq00=\", false, function() {\n    return [\n        _hooks_useMediaQueryState__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n_c = QuestionAndAnswers;\nvar _c;\n$RefreshReg$(_c, \"QuestionAndAnswers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QuestionAndAnswers/QuestionAndAnswers.tsx\n"));

/***/ })

});